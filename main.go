package main

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

type Song struct {
	Name    string `json:"name"`
	ID      int64  `json:"id"`
	Artists []struct {
		Name string `json:"name"`
	} `json:"artists"`
	Album struct {
		Name string `json:"name"`
	} `json:"album"`
}

type SearchResult struct {
	Result struct {
		Songs []Song `json:"songs"`
	} `json:"result"`
}

func getSignature(ts int64, secret string) string {
	s := strconv.FormatInt(ts, 10) + secret
	hash := md5.Sum([]byte(s))
	return hex.EncodeToString(hash[:])
}

func fetchKanxizaiURLwithLyric(songID string) (string, string, error) {
	secret := "kxz_163music_secret_key_2024"
	ts := time.Now().Unix()
	signature := getSignature(ts, secret)

	musicURL := "https://music.163.com/song?id=" + songID

	params := url.Values{}
	params.Set("action", "music")
	params.Set("url", musicURL)
	params.Set("level", "jymaster")
	params.Set("type", "json")
	params.Set("timestamp", strconv.FormatInt(ts, 10))
	params.Set("signature", signature)

	apiURL := "https://www.kanxizai.cn/163_music/api/api.php?" + params.Encode()

	log.Printf("[PLAY] 构造请求URL: %s", apiURL)

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		log.Printf("[PLAY][ERROR] 创建请求失败: %v", err)
		return "", "", err
	}
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36")
	req.Header.Set("Referer", "https://www.kanxizai.cn/")
	req.Header.Set("Origin", "https://www.kanxizai.cn/")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("[PLAY][ERROR] 执行请求失败: %v", err)
		return "", "", err
	}
	defer resp.Body.Close()
	log.Printf("[PLAY] 响应状态: %s", resp.Status)

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Printf("[PLAY][ERROR] 读取响应体失败: %v", err)
		return "", "", err
	}

	maxLogLen := 2000
	bodyPreview := string(body)
	if len(bodyPreview) > maxLogLen {
		bodyPreview = bodyPreview[:maxLogLen] + "...(截断)"
	}
	log.Printf("[PLAY] 响应体内容: %s", bodyPreview)

	var result struct {
		Code  int    `json:"code"`
		URL   string `json:"url"`
		Lyric string `json:"lyric"`
		Msg   string `json:"msg"`
	}
	if err := json.Unmarshal(body, &result); err != nil {
		log.Printf("[PLAY][ERROR] 解析JSON失败: %v, body=%s", err, body)
		return "", "", fmt.Errorf("parse json error: %v, body=%s", err, body)
	}
	if result.Code != 200 || result.URL == "" {
		log.Printf("[PLAY][ERROR] API返回失败: code=%d, msg=%s, url=%s", result.Code, result.Msg, result.URL)
		return "", "", fmt.Errorf("api failed: %v, msg=%s", result.Code, result.Msg)
	}
	log.Printf("[PLAY] 成功获取播放URL: %s", result.URL)
	return result.URL, result.Lyric, nil
}

func searchSongs(keyword string, page int) ([]Song, error) {
	start := time.Now()
	baseURL := "https://music.163.com/api/search/get/web"
	params := url.Values{}
	params.Set("s", keyword)
	params.Set("type", "1")
	params.Set("limit", "10")
	params.Set("offset", fmt.Sprintf("%d", (page-1)*10))
	reqURL := baseURL + "?" + params.Encode()
	log.Printf("[SEARCH] 构造请求 URL: %s", reqURL)

	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("GET", reqURL, nil)
	if err != nil {
		log.Printf("[SEARCH][ERROR] 创建请求失败: %v", err)
		return nil, err
	}
	req.Header.Set("Referer", "https://music.163.com")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	log.Printf("[SEARCH] 请求头: Referer=%s, User-Agent=%s", req.Header.Get("Referer"), req.Header.Get("User-Agent"))

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("[SEARCH][ERROR] 请求执行失败: %v", err)
		return nil, err
	}
	defer resp.Body.Close()
	log.Printf("[SEARCH] 响应状态: %s", resp.Status)

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Printf("[SEARCH][ERROR] 读取响应体失败: %v", err)
		return nil, err
	}

	maxLogLen := 2000
	bodyPreview := string(body)
	if len(bodyPreview) > maxLogLen {
		bodyPreview = bodyPreview[:maxLogLen] + "...(截断)"
	}
	log.Printf("[SEARCH] 响应体内容: %s", bodyPreview)

	var result SearchResult
	err = json.Unmarshal(body, &result)
	if err != nil {
		log.Printf("[SEARCH][ERROR] JSON解析失败: %v", err)
		return nil, err
	}
	elapsed := time.Since(start)
	log.Printf("[SEARCH] searchSongs 执行完成，耗时: %v，找到歌曲数: %d", elapsed, len(result.Result.Songs))
	return result.Result.Songs, nil
}

func searchHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("[HTTP] 收到搜索请求: %s %s from %s", r.Method, r.URL.String(), r.RemoteAddr)
	keyword := r.URL.Query().Get("q")
	pageStr := r.URL.Query().Get("page")
	if keyword == "" {
		log.Printf("[HTTP][WARN] 缺少参数 q")
		http.Error(w, "缺少参数 q", http.StatusBadRequest)
		return
	}
	page := 1
	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		} else {
			log.Printf("[HTTP][WARN] 参数 page 无效: %s", pageStr)
		}
	}
	log.Printf("[HTTP] 搜索关键词: %s, 页码: %d", keyword, page)
	songs, err := searchSongs(keyword, page)
	if err != nil {
		log.Printf("[HTTP][ERROR] 搜索失败: %v", err)
		http.Error(w, "搜索失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(songs); err != nil {
		log.Printf("[HTTP][ERROR] 写入响应失败: %v", err)
	}
}

func playHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("[HTTP] 收到播放请求: %s %s from %s", r.Method, r.URL.String(), r.RemoteAddr)
	songID := r.URL.Query().Get("id")
	if songID == "" {
		log.Printf("[HTTP][WARN] 缺少歌曲ID")
		http.Error(w, "缺少歌曲ID", http.StatusBadRequest)
		return
	}
	log.Printf("[HTTP] 解析到歌曲ID: %s", songID)
	url, lyric, err := fetchKanxizaiURLwithLyric(songID)
	if err != nil {
		log.Printf("[HTTP][ERROR] 获取播放地址失败: %v", err)
		http.Error(w, "无法获取播放地址: "+err.Error(), http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	resp := map[string]string{"url": url, "lyric": lyric}
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		log.Printf("[HTTP][ERROR] 写入音频 URL 响应失败: %v", err)
	}
	log.Printf("[HTTP] 成功获取并返回音频 URL (ID: %s): %s", songID, url)
}

func main() {
	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
	mux := http.NewServeMux()
	mux.HandleFunc("/search", searchHandler)
	mux.HandleFunc("/play", playHandler)
	fs := http.FileServer(http.Dir("./static"))
	mux.Handle("/", fs)
	log.Println("服务器启动： http://localhost:8086")
	log.Fatal(http.ListenAndServe(":8086", mux))
}
